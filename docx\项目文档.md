# 浓烟环境人体目标判别系统 - 项目文档

## 项目概述
本项目旨在开发一个基于YOLOv8的浓烟环境下人体检测模型，用于在烟雾环境中准确识别和定位人体目标。

## 项目结构
```
训练去烟后数据集的人体识别模型/
├── train_model.py          # 主训练脚本
├── dataset.yaml           # 数据集配置文件
├── dehazing.py           # 去雾处理脚本
├── wuxi_video_2_dehazed/ # 去雾后的数据集
│   ├── images/           # 图像文件
│   │   ├── train/       # 训练集 (2,245张)
│   │   ├── val/         # 验证集 (801张)
│   │   └── test/        # 测试集 (141张)
│   └── labels/          # 标签文件
├── yolov8n.pt           # YOLOv8n预训练权重
├── yolo11n.pt           # YOLO11n预训练权重
└── docx/                # 项目文档目录
    └── 项目文档.md       # 本文档
```

## 数据集信息
- **数据集名称**: wuxi_video_2_dehazed
- **数据集类型**: 去雾后的人体检测数据集
- **图像格式**: JPG
- **标签格式**: YOLO格式 (class x_center y_center width height)
- **类别数量**: 1 (person - 人体)
- **总图像数**: 3,187张
  - 训练集: 2,245张 (70.4%)
  - 验证集: 801张 (25.1%)
  - 测试集: 141张 (4.4%)

## 模型配置
- **基础模型**: YOLOv8n (nano版本)
- **输入尺寸**: 640x640
- **检测类别**: 单类别 (人体)
- **预训练权重**: COCO数据集预训练的YOLOv8n权重

## 训练超参数分析

### 当前参数设置
```python
epochs=1                    # 训练轮次
imgsz=640                  # 图像大小
batch=32                   # 批次大小
workers=2                  # 数据加载线程数
device='0'                 # GPU设备
lr0=0.001                  # 初始学习率
conf=0.25                  # 置信度阈值 (训练时无效)
iou=0.45                   # IoU阈值 (训练时无效)
single_cls=True            # 单类别模式
patience=50                # 早停耐心值
optimizer='Adam'           # 优化器
```

### 超参数问题分析

#### 严重问题
1. **epochs=1**: 训练轮次严重不足，无法让模型学习到有效特征
2. **conf和iou参数**: 这些是推理参数，在训练时会被忽略

#### 需要优化的参数
1. **batch=32**: 对于小数据集可能过大
2. **workers=2**: 数据加载线程数偏少
3. **lr0=0.001**: 学习率偏高
4. **optimizer='Adam'**: YOLOv8默认使用SGD

### 建议的参数设置
```python
epochs=100                 # 增加到100轮
batch=16                   # 减小批次大小
workers=4                  # 增加数据加载线程
lr0=0.0002                # 降低学习率
patience=15                # 调整早停耐心值
# 移除conf和iou参数
# 使用默认SGD优化器
```

## 代码问题修复

### 诊断发现的问题
1. **未使用的导入**: `matplotlib.pyplot as plt` 被导入但未使用

### 修复建议
- 移除未使用的matplotlib导入，或在代码中添加相应的可视化功能

## 训练流程
1. 加载YOLOv8n预训练模型
2. 使用dataset.yaml配置数据集路径
3. 执行模型训练
4. 在验证集上评估模型性能
5. 保存训练好的模型到model/human_detection_model目录

## 评估指标
- **mAP@0.5**: 在IoU阈值0.5下的平均精度
- **mAP@0.5:0.95**: 在IoU阈值0.5-0.95下的平均精度

## 输出文件
- 训练好的模型权重
- 训练过程图表
- 验证结果报告

## 使用说明
1. 确保已安装ultralytics库
2. 准备好GPU环境 (CUDA)
3. 运行训练脚本: `python train_model.py`
4. 查看训练结果和模型性能

## 注意事项
- 当前超参数设置存在严重问题，建议按照文档中的建议进行修正
- 训练前请确保数据集路径正确
- 建议在训练前备份重要数据

## 训练完成状态
✅ **训练已完成** - 100轮训练成功完成
- 训练时间: 32.01分钟
- 最佳模型: 第87轮 (mAP@0.5:0.95 = 94.64%)
- 最终性能: mAP@0.5:0.95 = 93.98%

## 相关文档
- 📊 [训练结果完整报告](./训练结果完整报告.md) - 详细的训练过程分析和性能评估
- 🔧 [技术规格与部署指南](./技术规格与部署指南.md) - 模型部署和使用指南
- 📈 训练损失曲线.png - 训练过程可视化图表
- 📈 性能指标曲线.png - 性能指标变化图表

## 模型文件
```
model/human_detection_model/weights/
├── best.pt     # 最佳模型权重 (5.94 MB) ⭐推荐使用
└── last.pt     # 最后一轮权重 (5.94 MB)
```

## 快速使用
```python
from ultralytics import YOLO

# 加载训练好的模型
model = YOLO('model/human_detection_model/weights/best.pt')

# 推理
results = model('your_image.jpg')
results[0].show()  # 显示结果
```

## 更新日志
- 2025-06-14: 创建项目文档，分析超参数问题并提出修正建议
- 2025-06-14: 完成100轮训练，生成完整训练报告和部署指南
